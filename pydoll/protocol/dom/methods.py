from enum import Enum


class DomMethod(str, Enum):
    DESCRIBE_NODE = 'DOM.describeNode'
    DISABLE = 'DOM.disable'
    ENABLE = 'DOM.enable'
    FOCUS = 'DOM.focus'
    GET_ATTRIBUTES = 'DOM.getAttributes'
    GET_BOX_MODEL = 'DOM.getBoxModel'
    GET_DOCUMENT = 'DOM.getDocument'
    GET_NODE_FOR_LOCATION = 'DOM.getNodeForLocation'
    GET_OUTER_HTML = 'DOM.getOuterHTML'
    HIDE_HIGHLIGHT = 'DOM.hideHighlight'
    HIGHLIGHT_NODE = 'DOM.highlightNode'
    HIGHLIGHT_RECT = 'DOM.highlightRect'
    MOVE_TO = 'DOM.moveTo'
    QUERY_SELECTOR = 'DOM.querySelector'
    QUERY_SELECTOR_ALL = 'DOM.querySelectorAll'
    REMOVE_ATTRIBUTE = 'DOM.removeAttribute'
    REMOVE_NODE = 'DOM.removeNode'
    REQUEST_CHILD_NODES = 'DOM.requestChildNodes'
    REQUEST_NODE = 'DOM.requestNode'
    RESOLVE_NODE = 'DOM.resolveNode'
    SCROLL_INTO_VIEW_IF_NEEDED = 'DOM.scrollIntoViewIfNeeded'
    SET_ATTRIBUTES_AS_TEXT = 'DOM.setAttributesAsText'
    SET_ATTRIBUTE_VALUE = 'DOM.setAttributeValue'
    SET_FILE_INPUT_FILES = 'DOM.setFileInputFiles'
    SET_NODE_NAME = 'DOM.setNodeName'
    SET_NODE_VALUE = 'DOM.setNodeValue'
    SET_OUTER_HTML = 'DOM.setOuterHTML'
    # Métodos experimentais
    COLLECT_CLASS_NAMES_FROM_SUBTREE = 'DOM.collectClassNamesFromSubtree'
    COPY_TO = 'DOM.copyTo'
    DISCARD_SEARCH_RESULTS = 'DOM.discardSearchResults'
    GET_ANCHOR_ELEMENT = 'DOM.getAnchorElement'
    GET_CONTAINER_FOR_NODE = 'DOM.getContainerForNode'
    GET_CONTENT_QUADS = 'DOM.getContentQuads'
    GET_DETACHED_DOM_NODES = 'DOM.getDetachedDomNodes'
    GET_ELEMENT_BY_RELATION = 'DOM.getElementByRelation'
    GET_FILE_INFO = 'DOM.getFileInfo'
    GET_FRAME_OWNER = 'DOM.getFrameOwner'
    GET_NODES_FOR_SUBTREE_BY_STYLE = 'DOM.getNodesForSubtreeByStyle'
    GET_NODE_STACK_TRACES = 'DOM.getNodeStackTraces'
    GET_QUERYING_DESCENDANTS_FOR_CONTAINER = 'DOM.getQueryingDescendantsForContainer'
    GET_RELAYOUT_BOUNDARY = 'DOM.getRelayoutBoundary'
    GET_SEARCH_RESULTS = 'DOM.getSearchResults'
    GET_TOP_LAYER_ELEMENTS = 'DOM.getTopLayerElements'
    MARK_UNDOABLE_STATE = 'DOM.markUndoableState'
    PERFORM_SEARCH = 'DOM.performSearch'
    PUSH_NODE_BY_PATH_TO_FRONTEND = 'DOM.pushNodeByPathToFrontend'
    PUSH_NODES_BY_BACKEND_IDS_TO_FRONTEND = 'DOM.pushNodesByBackendIdsToFrontend'
    REDO = 'DOM.redo'
    SET_INSPECTED_NODE = 'DOM.setInspectedNode'
    SET_NODE_STACK_TRACES_ENABLED = 'DOM.setNodeStackTracesEnabled'
    UNDO = 'DOM.undo'
