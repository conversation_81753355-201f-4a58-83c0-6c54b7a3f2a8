from enum import Enum


class PageMethod(str, Enum):
    ADD_SCRIPT_TO_EVALUATE_ON_NEW_DOCUMENT = 'Page.addScriptToEvaluateOnNewDocument'
    BRING_TO_FRONT = 'Page.bringToFront'
    CAPTURE_SCREENSHOT = 'Page.captureScreenshot'
    CLOSE = 'Page.close'
    CREATE_ISOLATED_WORLD = 'Page.createIsolatedWorld'
    DISABLE = 'Page.disable'
    ENABLE = 'Page.enable'
    GET_APP_MANIFEST = 'Page.getAppManifest'
    GET_FRAME_TREE = 'Page.getFrameTree'
    GET_LAYOUT_METRICS = 'Page.getLayoutMetrics'
    GET_NAVIGATION_HISTORY = 'Page.getNavigationHistory'
    HANDLE_JAVASCRIPT_DIALOG = 'Page.handleJavaScriptDialog'
    NAVIGATE = 'Page.navigate'
    NAVIGATE_TO_HISTORY_ENTRY = 'Page.navigateToHistoryEntry'
    PRINT_TO_PDF = 'Page.printToPDF'
    RELOAD = 'Page.reload'
    REMOVE_SCRIPT_TO_EVALUATE_ON_NEW_DOCUMENT = 'Page.removeScriptToEvaluateOnNewDocument'
    RESET_NAVIGATION_HISTORY = 'Page.resetNavigationHistory'
    SET_BYPASS_CSP = 'Page.setBypassCSP'
    SET_DOCUMENT_CONTENT = 'Page.setDocumentContent'
    SET_INTERCEPT_FILE_CHOOSER_DIALOG = 'Page.setInterceptFileChooserDialog'
    SET_LIFECYCLE_EVENTS_ENABLED = 'Page.setLifecycleEventsEnabled'
    STOP_LOADING = 'Page.stopLoading'
    ADD_COMPILATION_CACHE = 'Page.addCompilationCache'
    CAPTURE_SNAPSHOT = 'Page.captureSnapshot'
    CLEAR_COMPILATION_CACHE = 'Page.clearCompilationCache'
    CRASH = 'Page.crash'
    GENERATE_TEST_REPORT = 'Page.generateTestReport'
    GET_AD_SCRIPT_ANCESTRY_IDS = 'Page.getAdScriptAncestryIds'
    GET_APP_ID = 'Page.getAppId'
    GET_INSTALLABILITY_ERRORS = 'Page.getInstallabilityErrors'
    GET_ORIGIN_TRIALS = 'Page.getOriginTrials'
    GET_PERMISSIONS_POLICY_STATE = 'Page.getPermissionsPolicyState'
    GET_RESOURCE_CONTENT = 'Page.getResourceContent'
    GET_RESOURCE_TREE = 'Page.getResourceTree'
    PRODUCE_COMPILATION_CACHE = 'Page.produceCompilationCache'
    SCREENCAST_FRAME_ACK = 'Page.screencastFrameAck'
    SEARCH_IN_RESOURCE = 'Page.searchInResource'
    SET_AD_BLOCKING_ENABLED = 'Page.setAdBlockingEnabled'
    SET_FONT_FAMILIES = 'Page.setFontFamilies'
    SET_FONT_SIZES = 'Page.setFontSizes'
    SET_PRERENDERING_ALLOWED = 'Page.setPrerenderingAllowed'
    SET_RPH_REGISTRATION_MODE = 'Page.setRPHRegistrationMode'
    SET_SPC_TRANSACTION_MODE = 'Page.setSPCTransactionMode'
    SET_WEB_LIFECYCLE_STATE = 'Page.setWebLifecycleState'
    START_SCREENCAST = 'Page.startScreencast'
    STOP_SCREENCAST = 'Page.stopScreencast'
    WAIT_FOR_DEBUGGER = 'Page.waitForDebugger'
