from enum import Enum


class StorageMethod(str, Enum):
    CLEAR_COOKIES = 'Storage.clearCookies'
    CLEAR_DATA_FOR_ORIGIN = 'Storage.clearDataForOrigin'
    CLEAR_DATA_FOR_STORAGE_KEY = 'Storage.clearDataForStorageKey'
    GET_COOKIES = 'Storage.getCookies'
    GET_STORAGE_KEY_FOR_FRAME = 'Storage.getStorageKeyForFrame'
    GET_USAGE_AND_QUOTA = 'Storage.getUsageAndQuota'
    SET_COOKIES = 'Storage.setCookies'
    SET_PROTECTED_AUDIENCE_K_ANONYMITY = 'Storage.setProtectedAudienceKAnonymity'
    TRACK_CACHE_STORAGE_FOR_ORIGIN = 'Storage.trackCacheStorageForOrigin'
    TRACK_CACHE_STORAGE_FOR_STORAGE_KEY = 'Storage.trackCacheStorageForStorageKey'
    TRACK_INDEXED_DB_FOR_ORIGIN = 'Storage.trackIndexedDBForOrigin'
    TRACK_INDEXED_DB_FOR_STORAGE_KEY = 'Storage.trackIndexedDBForStorageKey'
    UNTRACK_CACHE_STORAGE_FOR_ORIGIN = 'Storage.untrackCacheStorageForOrigin'
    UNTRACK_CACHE_STORAGE_FOR_STORAGE_KEY = 'Storage.untrackCacheStorageForStorageKey'
    UNTRACK_INDEXED_DB_FOR_ORIGIN = 'Storage.untrackIndexedDBForOrigin'
    UNTRACK_INDEXED_DB_FOR_STORAGE_KEY = 'Storage.untrackIndexedDBForStorageKey'
    CLEAR_SHARED_STORAGE_ENTRIES = 'Storage.clearSharedStorageEntries'
    CLEAR_TRUST_TOKENS = 'Storage.clearTrustTokens'
    DELETE_SHARED_STORAGE_ENTRY = 'Storage.deleteSharedStorageEntry'
    DELETE_STORAGE_BUCKET = 'Storage.deleteStorageBucket'
    GET_AFFECTED_URLS_FOR_THIRD_PARTY_COOKIE_METADATA = (
        'Storage.getAffectedUrlsForThirdPartyCookieMetadata'
    )
    GET_INTEREST_GROUP_DETAILS = 'Storage.getInterestGroupDetails'
    GET_RELATED_WEBSITE_SETS = 'Storage.getRelatedWebsiteSets'
    GET_SHARED_STORAGE_ENTRIES = 'Storage.getSharedStorageEntries'
    GET_SHARED_STORAGE_METADATA = 'Storage.getSharedStorageMetadata'
    GET_TRUST_TOKENS = 'Storage.getTrustTokens'
    OVERRIDE_QUOTA_FOR_ORIGIN = 'Storage.overrideQuotaForOrigin'
    RESET_SHARED_STORAGE_BUDGET = 'Storage.resetSharedStorageBudget'
    RUN_BOUNCE_TRACKING_MITIGATIONS = 'Storage.runBounceTrackingMitigations'
    SEND_PENDING_ATTRIBUTION_REPORTS = 'Storage.sendPendingAttributionReports'
    SET_ATTRIBUTION_REPORTING_LOCAL_TESTING_MODE = 'Storage.setAttributionReportingLocalTestingMode'
    SET_ATTRIBUTION_REPORTING_TRACKING = 'Storage.setAttributionReportingTracking'
    SET_INTEREST_GROUP_AUCTION_TRACKING = 'Storage.setInterestGroupAuctionTracking'
    SET_INTEREST_GROUP_TRACKING = 'Storage.setInterestGroupTracking'
    SET_SHARED_STORAGE_ENTRY = 'Storage.setSharedStorageEntry'
    SET_SHARED_STORAGE_TRACKING = 'Storage.setSharedStorageTracking'
    SET_STORAGE_BUCKET_TRACKING = 'Storage.setStorageBucketTracking'
