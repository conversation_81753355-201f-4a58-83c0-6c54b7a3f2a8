# Protocol Events

This section documents the Chrome DevTools Protocol event constants and handlers used by Pydoll.

## Page Events

::: pydoll.protocol.page.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Network Events

::: pydoll.protocol.network.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## DOM Events

::: pydoll.protocol.dom.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Runtime Events

::: pydoll.protocol.runtime.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Fetch Events

::: pydoll.protocol.fetch.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Browser Events

::: pydoll.protocol.browser.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Target Events

::: pydoll.protocol.target.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Storage Events

::: pydoll.protocol.storage.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Input Events

::: pydoll.protocol.input.events
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3 