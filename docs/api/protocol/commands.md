# Protocol Commands

This section documents the Chrome DevTools Protocol command implementations used by Pydoll.

## Page Commands

::: pydoll.commands.page_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Runtime Commands

::: pydoll.commands.runtime_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## DOM Commands

::: pydoll.commands.dom_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Network Commands

::: pydoll.commands.network_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Input Commands

::: pydoll.commands.input_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Fetch Commands

::: pydoll.commands.fetch_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Browser Commands

::: pydoll.commands.browser_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Target Commands

::: pydoll.commands.target_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

## Storage Commands

::: pydoll.commands.storage_commands
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3 