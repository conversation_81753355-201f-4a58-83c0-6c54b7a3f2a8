name: Feature Request
description: Suggest a new feature or enhancement for pydoll
title: "[Feature Request]: "
labels: ["enhancement", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        # pydoll Feature Request
        
        Thank you for taking the time to suggest a new feature. This form will guide you through providing the information needed to consider your suggestion effectively.
  
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist before requesting
      description: Please make sure you've completed the following steps before submitting a feature request.
      options:
        - label: I have searched for [similar feature requests](https://github.com/thalissonvs/pydoll/issues) and didn't find a duplicate.
          required: true
        - label: I have checked the documentation to confirm this feature doesn't already exist.
          required: true
  
  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: Is your feature request related to a problem? Please describe what you're trying to accomplish.
      placeholder: I'm trying to accomplish X, but I'm unable to because Y...
    validations:
      required: true
  
  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see implemented. Be as specific as possible.
      placeholder: |
        I would like to see a new method/class that can...
        
        Example usage might look like:
        ```python
        client.new_feature(param1, param2)
        ```
    validations:
      required: true
  
  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Describe any alternative solutions or features you've considered.
      placeholder: I've tried accomplishing this using X and Y approaches, but they don't work well because...
  
  - type: textarea
    id: context
    attributes:
      label: Additional Context
      description: Add any other context, code examples, or references that might help explain your feature request.
      placeholder: |
        Other libraries like X and Y have similar features that work like...
        
        This would help users who need to...
  
  - type: dropdown
    id: importance
    attributes:
      label: Importance
      description: How important is this feature to your use case?
      options:
        - Nice to have
        - Important
        - Critical (blocking my usage)
    validations:
      required: true
  
  - type: dropdown
    id: contribution
    attributes:
      label: Contribution
      description: Would you be willing to contribute this feature yourself?
      options:
        - Yes, I'd be willing to implement this feature
        - I could help with parts of the implementation
        - No, I don't have the capacity to implement this 