name: Bug Report
description: Report a bug in pydoll
title: "[Bug]: "
labels: ["bug", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        # pydoll Bug Report
        
        Thank you for taking the time to report a bug. This form will guide you through providing the information needed to address the issue effectively.
  
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist before reporting
      description: Please make sure you've completed the following steps before submitting a bug report.
      options:
        - label: I have searched for [similar issues](https://github.com/thalissonvs/pydoll/issues) and didn't find a duplicate.
          required: true
        - label: I have updated to the latest version of pydoll to verify the issue still exists.
          required: true
  
  - type: input
    id: version
    attributes:
      label: pydoll Version
      description: What version of pydoll are you using when encountering this bug?
      placeholder: e.g., 1.3.2
    validations:
      required: true
  
  - type: input
    id: python_version
    attributes:
      label: Python Version
      description: What version of Python are you using?
      placeholder: e.g., 3.10.4
    validations:
      required: true
  
  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - Windows
        - macOS
        - Linux
        - Other (specify in environment details)
    validations:
      required: true
  
  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: When I try to use X feature, the library fails with error message Y...
    validations:
      required: true
  
  - type: textarea
    id: reproduction_steps
    attributes:
      label: Steps to Reproduce
      description: Step by step instructions to reproduce the bug.
      placeholder: |
        1. Import the library using `import pydoll`
        2. Set up the client with `...`
        3. Call method X with parameters Y
        4. See error
    validations:
      required: true
  
  - type: textarea
    id: code_example
    attributes:
      label: Code Example
      description: |
        A minimal, self-contained code example that demonstrates the issue.
        This will be automatically formatted into code, so no need for backticks.
      render: python
      placeholder: |
        from pydoll import Client
        
        client = Client(...)
        
        # Code that triggers the bug
        result = client.some_method(...)
        print(result)
    validations:
      required: true
  
  - type: textarea
    id: expected_behavior
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: The method should return X or perform Y...
    validations:
      required: false
  
  - type: textarea
    id: actual_behavior
    attributes:
      label: Actual Behavior
      description: What actually happened instead? Include full error messages and stack traces if applicable.
      placeholder: The method raised an exception...
    validations:
      required: false
  
  - type: textarea
    id: logs
    attributes:
      label: Relevant Log Output
      description: |
        If applicable, include any logs or error messages. 
        This will be automatically formatted, so no need for backticks.
      render: shell
      placeholder: |
        Traceback (most recent call last):
          File "example.py", line 10, in <module>
            ...
          File ".../pydoll/...", line N, in some_method
            ...
        SomeError: Error message
  
  - type: textarea
    id: additional_context
    attributes:
      label: Additional Context
      description: Add any other context about the problem here (environment details, potential causes, solutions you've tried, etc.)
      placeholder: I've tried reinstalling the package and using a different Python version, but the issue persists... 
