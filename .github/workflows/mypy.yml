name: MyPy CI

on:
  push:
    branches:
      - '*'         # matches every branch that doesn't contain a '/'
      - '*/*'       # matches every branch containing a single '/'
      - '**'        # matches every branch
  pull_request:

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      max-parallel: 4
      matrix:
        python-version: ["3.11"]

    steps:
      - uses: actions/checkout@v2

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install mypy
          python -m pip install -e .
          python -m mypy --install-types --non-interactive pydoll 

      - name: mypy
        run: python -m mypy .
