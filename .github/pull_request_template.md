<!-- 
Please choose the appropriate PR template for your change:

For bug fixes: .github/PULL_REQUEST_TEMPLATE/bug_fix.md
For refactoring: .github/PULL_REQUEST_TEMPLATE/refactoring.md
For releases: .github/PULL_REQUEST_TEMPLATE/release.md

Or use this general template for other types of changes.
-->

# Pull Request

## Description
<!-- Provide a clear and concise description of what this PR accomplishes -->

## Related Issue(s)
<!-- Link the issues that are being addressed by this PR. Use the format: "Fixes #123" or "Resolves #123" -->

## Type of Change
<!-- Check the appropriate options that apply to this PR -->
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Refactoring (no functional changes, no API changes)
- [ ] Performance improvement
- [ ] Tests (adding missing tests or correcting existing tests)
- [ ] Build or CI/CD related changes

## How Has This Been Tested?
<!-- Describe the tests you ran to verify your changes. Provide instructions so reviewers can reproduce. -->

```python
# Include code examples if relevant
```

## Testing Checklist
<!-- Check the testing aspects that apply to your change -->
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] All existing tests pass

## Screenshots
<!-- If applicable, add screenshots to help explain your changes -->

## Implementation Details
<!-- Provide any important details or context about the implementation -->

## API Changes
<!-- If applicable, describe any API changes -->

## Additional Info
<!-- Any additional information that might be useful for reviewers -->

## Checklist before requesting a review
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I have run `poetry run task lint` and fixed any issues
- [ ] I have run `poetry run task test` and all tests pass
- [ ] My commits follow the [conventional commits](https://www.conventionalcommits.org/) style 