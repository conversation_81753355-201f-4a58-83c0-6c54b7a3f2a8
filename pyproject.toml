[tool.poetry]
name = "pydoll-python"
version = "2.0.1"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [
    {include = "pydoll"}
]

[tool.poetry.dependencies]
python = "^3.10"
websockets = "^13.1"
aiohttp = "^3.9.5"
aiofiles = "^23.2.1"
bs4 = "^0.0.2"
mkdocstrings = "^0.29.1"


[tool.poetry.group.dev.dependencies]
ruff = "^0.7.1"
pytest = "^8.3.3"
taskipy = "^1.14.0"
pytest-asyncio = "^0.24.0"
pytest-cov = "^6.0.0"
aioresponses = "^0.7.7"
mkdocs = "^1.6.1"
mkdocs-material = "^9.6.11"
pymdown-extensions = "^10.14.3"
mkdocstrings = {extras = ["python"], version = "^0.29.1"}
griffe-typingdoc = "^0.2.8"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 100
target-version = "py310"


[tool.ruff.lint]
preview = true
select = ['I', 'F', 'E', 'W', 'PL', 'PT']
exclude = ['tests', 'tests/*']

[tool.ruff.format]
preview = true
quote-style = 'single'
docstring-code-format = true
docstring-code-line-length = 79
exclude = ['tests', 'tests/*']

[tool.pytest.ini_options]
pythonpath = "."
addopts = '-p no:warnings'

[tool.taskipy.tasks]
lint = 'ruff check .; ruff check . --diff'
format = 'ruff check . --fix; ruff format .'
test = 'pytest -s -x --cov=pydoll -vv'
post_test = 'coverage html'

[tool.mypy]
exclude = [
    "tests/",
]